import sensor, image, time
import math

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time = 2000)

# 设置自动增益和白平衡
sensor.set_auto_gain(False)  # 关闭自动增益以获得更稳定的图像
sensor.set_auto_whitebal(False)  # 关闭自动白平衡

clock = time.clock()

while(True):
    clock.tick()
    img = sensor.snapshot()

    # 转换为灰度图像进行处理
    img_gray = img.to_grayscale()

    # 高斯模糊去噪声（OpenMV版本）
    img_gray.gaussian(1)

    # 边缘检测
    img_edges = img_gray.find_edges(image.EDGE_CANNY, threshold=(50, 150))

    # 形态学操作：膨胀和腐蚀
    img_edges.dilate(1)  # 膨胀
    img_edges.erode(1)   # 腐蚀

    # 使用OpenMV的find_circles方法检测圆形
    circles = img.find_circles(threshold=3000, x_margin=10, y_margin=10, r_margin=10,
                              r_min=10, r_max=100, r_step=2)

    # 圆度阈值
    min_circularity = 0.85

    for circle in circles:
        # 计算圆的基本属性
        radius = circle.r()
        area = math.pi * radius * radius
        perimeter = 2 * math.pi * radius

        # 过滤掉小圆形
        if perimeter < 100 or area < 100:
            continue

        # 计算圆度(该指标是度量区域形状接近圆形的指标，值越接近1，形状越接近圆形)
        circularity = 4 * math.pi * area / (perimeter * perimeter)

        if circularity > min_circularity:
            # 在原图上画圆和标识圆心
            img.draw_circle(circle.x(), circle.y(), radius, color=(0, 255, 0), thickness=2)
            img.draw_cross(circle.x(), circle.y(), color=(0, 0, 255), size=10, thickness=2)

            # 可选：显示圆度信息
            img.draw_string(circle.x()-20, circle.y()-radius-20,
                          "C:%.2f" % circularity, color=(255, 255, 255))

    # 显示帧率
    print("FPS %f" % clock.fps())