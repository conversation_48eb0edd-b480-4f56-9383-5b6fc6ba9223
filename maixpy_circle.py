import sensor, image, time
import cv2
import numpy as np
import math

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)  # 使用RGB格式以便转换为OpenCV格式
sensor.set_framesize(sensor.QQVGA)  # 160x120，节省内存
sensor.skip_frames(time = 2000)

# 设置自动增益和白平衡
sensor.set_auto_gain(False)  # 关闭自动增益以获得更稳定的图像
sensor.set_auto_whitebal(False)  # 关闭自动白平衡

# OpenCV形态学操作核（减小核大小以节省内存）
kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3,3))

clock = time.clock()

while(True):
    clock.tick()
    img = sensor.snapshot()

    # 转换为numpy数组
    img_np = np.frombuffer(img.bytearray(), dtype=np.uint8)
    img_np = img_np.reshape((img.height(), img.width(), 2))  # RGB565格式

    # 转换为BGR格式（OpenCV标准）
    img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB5652BGR)

    # 转灰度
    gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)

    # 高斯模糊去噪声（减小核大小）
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)

    # 边缘检测，阈值 50，150
    edged = cv2.Canny(blurred, 50, 150)

    # 膨胀处理
    dilated = cv2.dilate(edged, kernel, iterations=1)

    # 腐蚀处理
    eroded = cv2.erode(dilated, kernel, iterations=1)

    # 找轮廓
    contours, _ = cv2.findContours(eroded, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 圆度阈值
    min_circularity = 0.8

    for cnt in contours:
        # 分别计算轮廓面积和周长
        area = cv2.contourArea(cnt)
        perimeter = cv2.arcLength(cnt, True)

        # 过滤掉小轮廓（适应小分辨率）
        if perimeter < 20 or area < 30:
            continue

        # 计算圆度(该指标是度量区域形状接近圆形的指标，值越接近1，形状越接近圆形)
        circularity = 4 * math.pi * area / (perimeter**2)
        if circularity > min_circularity:
            # 计算轮廓的最小外接圆
            (x, y), radius = cv2.minEnclosingCircle(cnt)
            center = (int(x), int(y))
            radius = int(radius)

            # 在OpenMV图像上绘制圆和圆心
            img.draw_circle(center[0], center[1], radius, color=(0, 255, 0), thickness=1)
            img.draw_cross(center[0], center[1], color=(0, 0, 255), size=5, thickness=1)

            # 显示圆度信息
            img.draw_string(center[0]-15, center[1]-radius-15,
                          "C:%.2f" % circularity, color=(255, 255, 255))

    # 显示帧率
    print("FPS %f" % clock.fps())