import sensor, image, time
import math

# 初始化摄像头 - 使用更小的分辨率以节省内存
sensor.reset()
sensor.set_pixformat(sensor.GRAYSCALE)  # 直接使用灰度格式节省内存
sensor.set_framesize(sensor.QQVGA)  # 160x120，更小的分辨率
sensor.skip_frames(time = 2000)

# 设置自动增益和白平衡
sensor.set_auto_gain(False)  # 关闭自动增益以获得更稳定的图像
sensor.set_auto_whitebal(False)  # 关闭自动白平衡

clock = time.clock()

while(True):
    clock.tick()
    img = sensor.snapshot()

    # 直接在原图上进行高斯模糊（就地操作，节省内存）
    img.gaussian(1)

    # 使用更简化的圆检测，减少内存使用
    circles = img.find_circles(threshold=2000, x_margin=5, y_margin=5, r_margin=5,
                              r_min=5, r_max=50, r_step=3)

    # 圆度阈值（稍微降低以适应简化的处理）
    min_circularity = 0.8

    for circle in circles:
        # 计算圆的基本属性
        radius = circle.r()
        area = math.pi * radius * radius
        perimeter = 2 * math.pi * radius

        # 调整过滤条件适应更小的分辨率
        if perimeter < 30 or area < 50:
            continue

        # 计算圆度(该指标是度量区域形状接近圆形的指标，值越接近1，形状越接近圆形)
        circularity = 4 * math.pi * area / (perimeter * perimeter)

        if circularity > min_circularity:
            # 在原图上画圆和标识圆心（使用单色以节省内存）
            img.draw_circle(circle.x(), circle.y(), radius, color=255, thickness=1)
            img.draw_cross(circle.x(), circle.y(), color=255, size=5, thickness=1)

            # 可选：显示圆度信息
            img.draw_string(circle.x()-15, circle.y()-radius-15,
                          "C:%.2f" % circularity, color=255)

    # 显示帧率
    print("FPS %f" % clock.fps())